import "dotenv/config";

export default {
  expo: {
    name: "resapp",
    slug: "resapp",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./src/assets/icon.png",
    userInterfaceStyle: "light",
    newArchEnabled: true,
    owner: process.env.EXPO_EAS_OWNER,
    splash: {
      image: "./src/assets/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff",
    },
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.anonymous.resapp",
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./src/assets/adaptive-icon.png",
        backgroundColor: "#ffffff",
      },
      package: "com.anonymous.resapp",
    },
    web: {
      favicon: "./src/assets/favicon.png",
    },
    extra: {
      API_URL: process.env.API_URL,
      API_KEY: process.env.API_KEY,
      eas: {
        projectId: process.env.EXPO_EAS_PROJECT_ID,
      },
    },
    plugins: [
      [
        "expo-image-picker",
        {
          photosPermission: {
            title: "Sabino <PERSON>",
            message:
              "Permítenos acceder a tu galería para seleccionar imágenes.",
            buttonPositive: "Ok",
          },
        },
      ],
    ],
  },
};
