import { useEffect, useState, useCallback } from "react";
import { Platform } from "react-native";
import * as Notifications from "expo-notifications";
import {
  notificationService,
  NotificationData,
} from "../services/notificationService";
import { usePushNotifications } from "./usePushNotifications";

export interface UseNotificationsReturn {
  // Token management
  expoPushToken: string | null;
  isTokenLoading: boolean;
  tokenError: string | null;

  // Permissions
  hasPermissions: boolean;
  requestPermissions: () => Promise<boolean>;

  // Token registration
  registerDeviceToken: () => Promise<void>;
  unregisterDeviceToken: () => Promise<void>;
  isRegistering: boolean;

  // Local notifications
  scheduleLocalNotification: (
    data: NotificationData,
    trigger?: Notifications.NotificationTriggerInput
  ) => Promise<string>;
  cancelNotification: (id: string) => Promise<void>;
  cancelAllNotifications: () => Promise<void>;

  // Badge management
  badgeCount: number;
  setBadgeCount: (count: number) => Promise<void>;
  clearBadgeAndNotifications: () => Promise<void>;

  // Notification state
  lastNotification: Notifications.Notification | null;
  lastNotificationResponse: Notifications.NotificationResponse | null;
}

export const useNotifications = (): UseNotificationsReturn => {
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
  const [isTokenLoading, setIsTokenLoading] = useState(false);
  const [tokenError, setTokenError] = useState<string | null>(null);
  const [hasPermissions, setHasPermissions] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [badgeCount, setBadgeCountState] = useState(0);
  const [lastNotification, setLastNotification] =
    useState<Notifications.Notification | null>(null);
  const [lastNotificationResponse, setLastNotificationResponse] =
    useState<Notifications.NotificationResponse | null>(null);

  const { registerToken, unregisterToken } = usePushNotifications();

  // Inicializar el servicio de notificaciones
  useEffect(() => {
    const initializeNotifications = async () => {
      // Configurar canal de Android
      await notificationService.setupAndroidChannel();

      // Obtener badge count inicial
      const initialBadgeCount = await notificationService.getBadgeCount();
      setBadgeCountState(initialBadgeCount);

      // Verificar permisos existentes
      const { status } = await Notifications.getPermissionsAsync();
      setHasPermissions(status === "granted");

      // Si ya tiene permisos, obtener token
      if (status === "granted") {
        await getExpoPushToken();
      }
    };

    initializeNotifications();

    // Configurar listeners para notificaciones
    const notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        setLastNotification(notification);
      }
    );

    const responseListener =
      Notifications.addNotificationResponseReceivedListener((response) => {
        setLastNotificationResponse(response);
      });

    // Cleanup
    return () => {
      notificationListener?.remove();
      responseListener?.remove();
      notificationService.cleanup();
    };
  }, []);

  const getExpoPushToken = useCallback(async () => {
    setIsTokenLoading(true);
    setTokenError(null);

    try {
      const token = await notificationService.getExpoPushToken();
      setExpoPushToken(token);
      setHasPermissions(!!token);
      return token;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Error getting push token";
      setTokenError(errorMessage);
      console.error("Error getting push token:", error);
      return null;
    } finally {
      setIsTokenLoading(false);
    }
  }, []);

  const requestPermissions = useCallback(async (): Promise<boolean> => {
    try {
      const granted = await notificationService.requestPermissions();
      setHasPermissions(granted);

      if (granted) {
        await getExpoPushToken();
      }

      return granted;
    } catch (error) {
      console.error("Error requesting permissions:", error);
      return false;
    }
  }, [getExpoPushToken]);

  const registerDeviceToken = useCallback(async () => {
    if (!expoPushToken) {
      console.log("No push token available");
      return;
    }

    setIsRegistering(true);
    try {
      const device = Platform.OS === "ios" ? "iOS" : "Android";
      await registerToken.mutateAsync({ token: expoPushToken, device });
      console.log("Device token registered successfully");
    } catch (error) {
      console.error("Error registering device token:", error);
      throw error;
    } finally {
      setIsRegistering(false);
    }
  }, [expoPushToken, registerToken]);

  const unregisterDeviceToken = useCallback(async () => {
    if (!expoPushToken) {
      console.log("No push token to unregister");
      return;
    }

    setIsRegistering(true);
    try {
      console.log("Unregistering device token...");
      // TODO: Implementar lógica para obtener el ID del token y desregistrarlo
      // const tokenId = getUserTokenId(expoPushToken);
      // await unregisterToken.mutateAsync(tokenId);
    } catch (error) {
      console.error("Error unregistering device token:", error);
      throw error;
    } finally {
      setIsRegistering(false);
    }
  }, [expoPushToken, unregisterToken]);

  const scheduleLocalNotification = useCallback(
    async (
      data: NotificationData,
      trigger?: Notifications.NotificationTriggerInput
    ): Promise<string> => {
      return await notificationService.scheduleLocalNotification(data, trigger);
    },
    []
  );

  const cancelNotification = useCallback(async (id: string) => {
    await notificationService.cancelNotification(id);
  }, []);

  const cancelAllNotifications = useCallback(async () => {
    await notificationService.cancelAllNotifications();
  }, []);

  const setBadgeCount = useCallback(async (count: number) => {
    await notificationService.setBadgeCount(count);
    setBadgeCountState(count);
  }, []);

  const clearBadgeAndNotifications = useCallback(async () => {
    await notificationService.clearBadgeAndNotifications();
    setBadgeCountState(0);
  }, []);

  return {
    // Token management
    expoPushToken,
    isTokenLoading,
    tokenError,

    // Permissions
    hasPermissions,
    requestPermissions,

    // Token registration
    registerDeviceToken,
    unregisterDeviceToken,
    isRegistering,

    // Local notifications
    scheduleLocalNotification,
    cancelNotification,
    cancelAllNotifications,

    // Badge management
    badgeCount,
    setBadgeCount,
    clearBadgeAndNotifications,

    // Notification state
    lastNotification,
    lastNotificationResponse,
  };
};
