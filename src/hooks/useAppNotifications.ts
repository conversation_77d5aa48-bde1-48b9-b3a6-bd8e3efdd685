import { useCallback } from "react";
import { useNotifications } from "./useNotifications";

/**
 * Hook personalizado para notificaciones específicas de la aplicación ResApp
 * Proporciona métodos convenientes para enviar notificaciones contextuales
 */
export const useAppNotifications = () => {
  const { scheduleLocalNotification } = useNotifications();

  // Notificaciones relacionadas con propiedades
  const notifyNewComplaint = useCallback(async (complaintId: string) => {
    return await scheduleLocalNotification({
      title: "Nueva queja registrada",
      body: "Tu queja ha sido registrada exitosamente",
      data: { 
        screen: "PropertyComplaints", 
        complaintId,
        action: "view_complaint" 
      },
    });
  }, [scheduleLocalNotification]);

  const notifyMaintenanceUpdate = useCallback(async (reportId: string, status: string) => {
    return await scheduleLocalNotification({
      title: "Actualización de mantenimiento",
      body: `Tu reporte de mantenimiento está ${status}`,
      data: { 
        screen: "PropertyMaintenanceReports", 
        reportId,
        action: "view_report" 
      },
    });
  }, [scheduleLocalNotification]);

  const notifyNewFine = useCallback(async (fineId: string, amount: number) => {
    return await scheduleLocalNotification({
      title: "Nueva multa registrada",
      body: `Se ha registrado una multa de $${amount}`,
      data: { 
        screen: "PropertyFines", 
        fineId,
        action: "view_fine" 
      },
    });
  }, [scheduleLocalNotification]);

  // Notificaciones de reservas
  const notifyReservationConfirmed = useCallback(async (reservationId: string, facilityName: string) => {
    return await scheduleLocalNotification({
      title: "Reserva confirmada",
      body: `Tu reserva en ${facilityName} ha sido confirmada`,
      data: { 
        screen: "PropertyReservations", 
        reservationId,
        action: "view_reservation" 
      },
    });
  }, [scheduleLocalNotification]);

  const notifyReservationReminder = useCallback(async (
    reservationId: string, 
    facilityName: string, 
    reminderTime: Date
  ) => {
    return await scheduleLocalNotification(
      {
        title: "Recordatorio de reserva",
        body: `Tu reserva en ${facilityName} es en 1 hora`,
        data: { 
          screen: "PropertyReservations", 
          reservationId,
          action: "view_reservation" 
        },
      },
      {
        date: reminderTime,
      }
    );
  }, [scheduleLocalNotification]);

  // Notificaciones de pagos
  const notifyPaymentDue = useCallback(async (chargeId: string, amount: number, dueDate: string) => {
    return await scheduleLocalNotification({
      title: "Pago pendiente",
      body: `Tienes un pago de $${amount} venciendo el ${dueDate}`,
      data: { 
        screen: "PropertyMonthlyCharges", 
        chargeId,
        action: "view_charge" 
      },
    });
  }, [scheduleLocalNotification]);

  const notifyPaymentReceived = useCallback(async (paymentId: string, amount: number) => {
    return await scheduleLocalNotification({
      title: "Pago recibido",
      body: `Tu pago de $${amount} ha sido procesado exitosamente`,
      data: { 
        screen: "PaymentsList", 
        paymentId,
        action: "view_payment" 
      },
    });
  }, [scheduleLocalNotification]);

  // Notificaciones de paquetes
  const notifyPackageDelivered = useCallback(async (packageId: string, description: string) => {
    return await scheduleLocalNotification({
      title: "Paquete entregado",
      body: `Tu paquete "${description}" ha sido entregado`,
      data: { 
        screen: "PropertyPackages", 
        packageId,
        action: "view_package" 
      },
    });
  }, [scheduleLocalNotification]);

  const notifyPackageReadyForPickup = useCallback(async (packageId: string, description: string) => {
    return await scheduleLocalNotification({
      title: "Paquete listo para recoger",
      body: `Tu paquete "${description}" está listo para recoger`,
      data: { 
        screen: "PropertyPackages", 
        packageId,
        action: "generate_token" 
      },
    });
  }, [scheduleLocalNotification]);

  // Notificaciones generales
  const notifyAnnouncement = useCallback(async (title: string, message: string) => {
    return await scheduleLocalNotification({
      title: "Nuevo anuncio",
      body: message,
      data: { 
        screen: "Dashboard",
        action: "view_announcements" 
      },
    });
  }, [scheduleLocalNotification]);

  const notifyEmergency = useCallback(async (message: string) => {
    return await scheduleLocalNotification({
      title: "🚨 Emergencia",
      body: message,
      data: { 
        screen: "Dashboard",
        action: "emergency_info" 
      },
      sound: true,
    });
  }, [scheduleLocalNotification]);

  // Recordatorios programados
  const scheduleMaintenanceReminder = useCallback(async (
    reportId: string,
    reminderDate: Date
  ) => {
    return await scheduleLocalNotification(
      {
        title: "Recordatorio de mantenimiento",
        body: "No olvides hacer seguimiento a tu reporte de mantenimiento",
        data: { 
          screen: "PropertyMaintenanceReports", 
          reportId,
          action: "follow_up" 
        },
      },
      {
        date: reminderDate,
      }
    );
  }, [scheduleLocalNotification]);

  const schedulePaymentReminder = useCallback(async (
    chargeId: string,
    amount: number,
    reminderDate: Date
  ) => {
    return await scheduleLocalNotification(
      {
        title: "Recordatorio de pago",
        body: `Recuerda que tienes un pago pendiente de $${amount}`,
        data: { 
          screen: "PropertyMonthlyCharges", 
          chargeId,
          action: "pay_now" 
        },
      },
      {
        date: reminderDate,
      }
    );
  }, [scheduleLocalNotification]);

  return {
    // Notificaciones de propiedades
    notifyNewComplaint,
    notifyMaintenanceUpdate,
    notifyNewFine,
    
    // Notificaciones de reservas
    notifyReservationConfirmed,
    notifyReservationReminder,
    
    // Notificaciones de pagos
    notifyPaymentDue,
    notifyPaymentReceived,
    
    // Notificaciones de paquetes
    notifyPackageDelivered,
    notifyPackageReadyForPickup,
    
    // Notificaciones generales
    notifyAnnouncement,
    notifyEmergency,
    
    // Recordatorios programados
    scheduleMaintenanceReminder,
    schedulePaymentReminder,
  };
};
