import { AppNavigator } from "./navigation/AppNavigator";
import { StatusBar } from "expo-status-bar";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "./context/AuthContext";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { configureCalendarLocale } from "./config/calendar-locale";
import { NotificationHandler } from "./components/NotificationHandler";

configureCalendarLocale();

const queryClient = new QueryClient();

export default () => {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <StatusBar animated />
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <NotificationHandler>
            <AppNavigator />
          </NotificationHandler>
        </AuthProvider>
      </QueryClientProvider>
    </GestureHandlerRootView>
  );
};
