import React, { useEffect } from "react";
import { useNotifications } from "../hooks/useNotifications";
import { notificationService } from "../services/notificationService";

interface NotificationHandlerProps {
  children: React.ReactNode;
}

/**
 * Componente que maneja las notificaciones globalmente en la aplicación
 * Debe ser colocado en el nivel superior de la app para funcionar correctamente
 */
export const NotificationHandler: React.FC<NotificationHandlerProps> = ({
  children,
}) => {
  const {
    lastNotification,
    lastNotificationResponse,
    clearBadgeAndNotifications,
  } = useNotifications();

  useEffect(() => {
    // Configurar el canal de Android al iniciar la app
    notificationService.setupAndroidChannel();
  }, []);

  useEffect(() => {
    if (lastNotification) {
      console.log("New notification received:", lastNotification);

      // Aquí puedes agregar lógica personalizada para manejar notificaciones
      // Por ejemplo, actualizar el estado de la app, mostrar alertas, etc.

      const { title, body, data } = lastNotification.request.content;
      console.log(`Notification: ${title} - ${body}`, data);
    }
  }, [lastNotification]);

  useEffect(() => {
    if (lastNotificationResponse) {
      console.log("Notification tapped:", lastNotificationResponse);

      // Manejar la navegación basada en la notificación
      const { data } = lastNotificationResponse.notification.request.content;

      if (data?.screen && typeof data.screen === "string") {
        console.log(`Should navigate to: ${data.screen}`);
        // Aquí puedes implementar la navegación usando React Navigation
        // Por ejemplo: navigation.navigate(data.screen, data.params);
      }

      if (data?.action && typeof data.action === "string") {
        console.log(`Should perform action: ${data.action}`);
        // Aquí puedes implementar acciones específicas
      }

      // Limpiar notificaciones cuando el usuario interactúa con ellas
      clearBadgeAndNotifications();
    }
  }, [lastNotificationResponse, clearBadgeAndNotifications]);

  return <>{children}</>;
};
