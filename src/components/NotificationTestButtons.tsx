import React from "react";
import { View, StyleSheet, Alert } from "react-native";
import { useNotifications } from "../hooks/useNotifications";
import { Button } from "./main";

/**
 * Componente de prueba para testing de notificaciones
 * Solo para desarrollo - remover en producción
 */
export const NotificationTestButtons: React.FC = () => {
  const {
    scheduleLocalNotification,
    setBadgeCount,
    clearBadgeAndNotifications,
    badgeCount,
    expoPushToken,
  } = useNotifications();

  const sendImmediateNotification = async () => {
    try {
      const notificationId = await scheduleLocalNotification({
        title: "¡Notificación de prueba!",
        body: "Esta es una notificación inmediata de prueba",
        data: { screen: "Home", action: "test" },
        sound: true,
      });

      Alert.alert("Éxito", `Notificación enviada con ID: ${notificationId}`);
    } catch (error) {
      Alert.alert("Error", "No se pudo enviar la notificación");
      console.error("Error sending notification:", error);
    }
  };

  const sendDelayedNotification = async () => {
    try {
      const notificationId = await scheduleLocalNotification(
        {
          title: "Notificación programada",
          body: "Esta notificación se programó para 10 segundos después",
          data: { screen: "Notifications", action: "delayed" },
          sound: true,
        },
        {
          seconds: 10,
        }
      );

      Alert.alert(
        "Programada",
        `Notificación programada para 10 segundos con ID: ${notificationId}`
      );
    } catch (error) {
      Alert.alert("Error", "No se pudo programar la notificación");
      console.error("Error scheduling notification:", error);
    }
  };

  const increaseBadge = async () => {
    const newCount = badgeCount + 1;
    await setBadgeCount(newCount);
    Alert.alert("Badge actualizado", `Nuevo badge count: ${newCount}`);
  };

  const clearAll = async () => {
    await clearBadgeAndNotifications();
    Alert.alert("Limpiado", "Badge y notificaciones limpiadas");
  };

  const showToken = () => {
    if (expoPushToken) {
      Alert.alert("Push Token", expoPushToken);
    } else {
      Alert.alert("Sin Token", "No hay push token disponible");
    }
  };

  return (
    <View style={styles.container}>
      <Button
        title="Notificación Inmediata"
        onPress={sendImmediateNotification}
        style={styles.button}
      />

      <Button
        title="Notificación en 10s"
        onPress={sendDelayedNotification}
        style={styles.button}
      />

      <Button
        title={`Aumentar Badge (${badgeCount})`}
        onPress={increaseBadge}
        style={styles.button}
      />

      <Button title="Limpiar Todo" onPress={clearAll} style={styles.button} />

      <Button title="Mostrar Token" onPress={showToken} style={styles.button} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    gap: 10,
  },
  button: {
    marginVertical: 5,
  },
});
