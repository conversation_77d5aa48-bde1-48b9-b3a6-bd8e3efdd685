import { Platform, Text } from "react-native";
import { Loading } from "../../components/Loading";
import { QUERIES } from "../../constants/queries";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { GradientView } from "../../components/layouts/GradientView";
import * as Notifications from "expo-notifications";
import { NotificationSection } from "../../components/Sections/NotificationSection";
import { useEffect, useState } from "react";
import { usePushNotifications } from "../../hooks/usePushNotifications";
import { config } from "../../config/configuration";

export const NotificationsConfig: React.FC = () => {
  const projectId = config.easProjectId;
  const {
    data: userData,
    isLoading,
    error,
    refetch: refetchUserData,
  } = useCachedQuery<Me>(QUERIES.ME);
  const { registerToken, unregisterToken } = usePushNotifications();
  const [deviceToken, setDeviceToken] = useState<string | undefined>();

  useEffect(() => {
    registerForPushNotificationsAsync();
  }, []);

  if (isLoading) return <Loading />;
  if (error) return <Text>Error</Text>;
  if (!userData) return <Text>Sin datos</Text>;

  const { pushTokens } = userData || {};

  const device = Platform.OS === "ios" ? "iOS" : "Android";

  const registerForPushNotificationsAsync = async () => {
    let token;
    if (device === "iOS") {
      await Notifications.requestPermissionsAsync();
    }
    try {
      token = (
        await Notifications.getExpoPushTokenAsync({
          projectId,
        })
      ).data;
      setDeviceToken(token);
    } catch (error) {
      console.log(error);
    }
  };

  const tokenEnabled = pushTokens?.find((token) => token.token === deviceToken);

  const handlePushToggle = async () => {
    if (tokenEnabled) {
      const tokenToDelete = pushTokens?.find(
        (token) => token.token === deviceToken
      )?.id;
      if (tokenToDelete) {
        await unregisterToken.mutateAsync(tokenToDelete);
      }
    } else {
      if (!deviceToken) return;
      console.log("Registering token", device);
      //   registerToken.mutateAsync({ token: deviceToken, device });
    }
    // refetchUserData();
  };

  return (
    <GradientView firstLineText="Configuración de Notificaciones">
      <NotificationSection
        isEnabled={tokenEnabled !== undefined}
        onToggle={() => handlePushToggle()}
        title="Notificaciones push"
        label="Notificaciones push"
      />
      <NotificationSection
        isEnabled={true}
        onToggle={() => console.log("change")}
        title="Notificaciones de correo"
        label="Notificaciones de correo"
      />
    </GradientView>
  );
};
