import { useState, useMemo } from "react";
import { View, StyleSheet, ScrollView, Alert, Text } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { z } from "zod";
import { useZodForm } from "../../hooks/useZodForm";
import { TextAreaFormField } from "../../components/forms/TextAreaFormField";
import { ImagePickerField } from "../../components/forms/ImagePickerField";
import { GradientView } from "../../components/layouts/GradientView";
import { Button } from "../../components";
import { theme } from "../../theme";
import { useMaintenanceReports } from "../../hooks/useMaintenanceReports";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { QUERIES } from "../../constants/queries";
import { CreateMaintenanceIssueReport } from "../../interfaces/maintenance-issue-report";
import { ComplaintConfirmModal } from "../../components/modals/ComplaintConfirmModal";
import { SuccessModal } from "../../components/modals/SuccessModal";
import { ErrorModal } from "../../components/modals/ErrorModal";
import { LoadingOverlay } from "../../components/LoadingOverlay";
import { useAppNotifications } from "../../hooks/useAppNotifications";

const maintenanceReportSchema = z.object({
  description: z
    .string({ required_error: "Descripción requerida" })
    .min(10, "Mínimo 10 caracteres"),
  images: z.array(z.string()).min(1, "Agrega al menos una imagen"),
});

type MaintenanceReportFormValues = z.infer<typeof maintenanceReportSchema>;

interface MaintenanceConfirmData {
  description: string;
  imagesCount: number;
}

export const CreateMaintenanceReportScreen: React.FC = () => {
  const navigation = useNavigation();
  const me = useCachedQuery<Me>(QUERIES.ME);
  const propertyId = me.data?.properties[0].id ?? "";

  // Estados para modales
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Hooks
  const { facilities, createMaintenanceReport } = useMaintenanceReports();
  const { notifyMaintenanceUpdate } = useAppNotifications();

  const { control, handleSubmit, watch } = useZodForm(maintenanceReportSchema, {
    defaultValues: {
      description: "",
      images: [],
    },
  });

  // Observar valores del formulario para el modal de confirmación
  const watchedValues = watch();

  const onSubmit = (data: MaintenanceReportFormValues) => {
    // Validación adicional
    if (!propertyId) {
      Alert.alert("Error", "No se pudo obtener la información de la propiedad");
      return;
    }

    if (data.images.length === 0) {
      Alert.alert("Error", "Agrega al menos una imagen del problema");
      return;
    }

    // Mostrar modal de confirmación
    setShowConfirmModal(true);
  };

  // Función para confirmar el reporte
  const handleConfirmReport = async () => {
    if (!propertyId) return;

    try {
      const reportData: CreateMaintenanceIssueReport = {
        propertyId,
        description: watchedValues.description,
        images: watchedValues.images,
      };

      const newReport = await createMaintenanceReport.mutateAsync(reportData);

      // Enviar notificación de confirmación
      try {
        await notifyMaintenanceUpdate(newReport.id, "registrado");
      } catch (notificationError) {
        console.warn("Error enviando notificación:", notificationError);
        // No interrumpir el flujo si falla la notificación
      }

      // Cerrar modal de confirmación y mostrar éxito
      setShowConfirmModal(false);
      setShowSuccessModal(true);
    } catch (error: any) {
      // Cerrar modal de confirmación y mostrar error
      setShowConfirmModal(false);
      setErrorMessage(
        error?.response?.data?.message ??
          "Error al crear el reporte de mantenimiento"
      );
      setShowErrorModal(true);
    }
  };

  // Función para cerrar modal de éxito y navegar de vuelta
  const handleSuccessClose = () => {
    setShowSuccessModal(false);
    navigation.goBack();
  };

  // Función para cerrar modal de error
  const handleErrorClose = () => {
    setShowErrorModal(false);
  };

  // Preparar datos para el modal de confirmación
  const confirmData: MaintenanceConfirmData | null = useMemo(() => {
    return {
      description: watchedValues.description,
      imagesCount: watchedValues.images?.length || 0,
    };
  }, [watchedValues, facilities.data]);

  return (
    <GradientView firstLineText="Reporte de Mantenimiento">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          <TextAreaFormField
            name="description"
            control={control}
            placeholder="Describe detalladamente el problema de mantenimiento..."
            numberOfLines={6}
          />

          <ImagePickerField
            name="images"
            control={control}
            label="Imágenes del problema (requeridas)"
            maxImages={5}
          />

          <Button
            title="Enviar Reporte"
            onPress={handleSubmit(onSubmit)}
            disabled={createMaintenanceReport.isPending}
          />
          <Text style={styles.message}>
            Este formulario sirve para reportar un problema de mantenimiento.
          </Text>
        </View>
      </ScrollView>

      {/* Loading Overlay */}
      <LoadingOverlay visible={createMaintenanceReport.isPending} />

      {/* Modal de Confirmación */}
      {confirmData && (
        <ComplaintConfirmModal
          visible={showConfirmModal}
          complaintData={{
            detail: confirmData.description,
            imagesCount: confirmData.imagesCount,
          }}
          onConfirm={handleConfirmReport}
          onCancel={() => setShowConfirmModal(false)}
          isLoading={createMaintenanceReport.isPending}
          title="Confirmar Reporte"
        />
      )}

      {/* Modal de Éxito */}
      <SuccessModal
        visible={showSuccessModal}
        title="¡Reporte Enviado!"
        message="Tu reporte de mantenimiento ha sido registrado exitosamente. El equipo de mantenimiento lo revisará pronto."
        onClose={handleSuccessClose}
        buttonText="Continuar"
      />

      {/* Modal de Error */}
      <ErrorModal
        visible={showErrorModal}
        title="Error al Enviar Reporte"
        message={errorMessage}
        onClose={handleErrorClose}
        buttonText="Intentar de nuevo"
      />
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  form: {
    padding: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  message: {
    color: theme.colors.gray500,
    fontSize: theme.fontSizes.sm,
    paddingHorizontal: theme.spacing.xl,
    textAlign: "center",
  },
});
